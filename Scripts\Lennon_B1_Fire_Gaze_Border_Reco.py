import os
import csv
import numpy as np
import matplotlib.pyplot as plt
from scipy.stats import gaussian_kde
from tqdm import tqdm

"""
识别受试者对于火焰的眼动注视概率方程和概率边界
Modified 2025-08-04
"""

sub_folder_path = "B1_Border_OnFire_AtAllPos"

infos_all = []
local_pos_all = []

target_dir_items =[
    ["alldooropen_1", "lgg"],
    ["alldooropen_1", "qyy"],
    ["alldooropen_3", "ayk"],
    ["alldooropen_3", "cxy"],
    ["alldooropen_3", "hcw"],
    ["alldooropen_3", "lzy"],
    ["alldooropen_3", "wty"],
    ["alldooropen_3", "yzn"],
    ["alldooropen_3", "zzy"],
    ["alldooropen_3", "xxk"],
    ["halfdooropen_3", "hy"],
    ["halfdooropen_3", "wxk"],
    ["onedooropen_3", "dzc1"],
    ["onedooropen_3", "xwh"],
]


def get_grand_parent_folder():
    current_script_path = os.path.abspath(__file__)
    current_folder = os.path.dirname(current_script_path)
    parent_folder = os.path.dirname(current_folder)
    return parent_folder


def get_all_file_paths(directory):
    file_paths = []
    for root, directories, files in os.walk(directory):
        for filename in files:
            file_full_path = os.path.join(root, filename)
            print(file_full_path)
            if "csv" in file_full_path.lower():
                if "subject" in file_full_path.lower():
                    for path_item in target_dir_items:
                        if path_item[0].replace("_", "-") in file_full_path and path_item[1] in file_full_path:
                            file_paths.append(file_full_path)
                            print(f"Find Target File With Turnover: {file_full_path} Count:{len(file_paths)}")
    return file_paths


def get_local_position(
    x_a: float,
    y_a: float,
    rotation_a: float,
    x_b: float,
    y_b: float,
) -> np.ndarray:
    rotation_rad = np.radians(rotation_a)
    translated = np.array([x_b - x_a, y_b - y_a])
    rot_mat = np.array(
        [[np.cos(rotation_rad), -np.sin(rotation_rad)],
         [np.sin(rotation_rad),  np.cos(rotation_rad)]]
    )
    return rot_mat @ translated


def build_data_ref():
    global infos_all, local_pos_all

    # 第一轮遍历，构建原始全局<轨迹数据>与<眼动数据>之间的映射关系
    files = get_all_file_paths(get_grand_parent_folder() + os.sep + "DataProcess" + os.sep + "FirstExp" + os.sep + "GroupHaveNPC")
    print(get_grand_parent_folder() + os.sep + "DataProcess" + os.sep + "FirstExp" + os.sep + "GroupHaveNPC")
    print(len(files))
    progress_bar = tqdm(total=len(files), desc="读取 CSV", dynamic_ncols=True)

    for file in files:
        dict_subject, dict_gaze, dict_hit_obj = {}, {}, {}

        with open(file, "r", newline="", encoding="utf-8", errors="ignore") as f:
            reader = csv.reader(f)
            next(reader, None)        # 跳过表头
            for row in reader:
                if len(row) > 3:
                    dict_subject[row[0]] = [row[1], row[3]]

        gaze_file = file.replace("Subject", "EyeGaze")
        with open(gaze_file, "r", newline="", encoding="utf-8", errors="ignore") as f:
            reader = csv.reader(f)
            next(reader, None)
            for row in reader:
                if len(row) > 9:
                    dict_gaze[row[0]] = [row[2], row[7], row[9]]
                    dict_hit_obj[row[0]] = row[10]

        infos_all += get_data(dict_subject, dict_gaze, dict_hit_obj)
        progress_bar.update(1)

    progress_bar.close()

    # 基于既有的data_pairs 计算相对坐标
    progress_bar = tqdm(total=len(infos_all), desc="计算相对坐标", dynamic_ncols=True)
    for sub_x, sub_y, sub_rot, gaze_x, gaze_y in infos_all:
        local_pos_all.append(get_local_position(sub_x, sub_y, sub_rot, gaze_x, gaze_y))
        progress_bar.update(1)
    progress_bar.close()


def get_data(subject_container, gaze_container, hit_container):
    raw_infos = []
    for key in subject_container.keys():
        if key not in gaze_container:
            print(f"Error! Key {key} not found in gaze container.")
            continue
        sub_x, sub_y = map(float, subject_container[key])
        sub_rot, gaze_x, gaze_y = map(float, gaze_container[key])
        hit_name = hit_container[key].lower()
        if "fire" in hit_name:
            raw_infos.append([sub_x, sub_y, sub_rot, gaze_x, gaze_y])
    return raw_infos


def execute_analyze(data_container, name):
    print(f">>>> {name} 数据点数：{len(data_container)}")

    points = np.array(data_container).T
    if points.shape[1] < 2:
        print("数据点不足，跳过KDE计算")
        return

    # 开始！梭哈！KDE！
    print("开始计算二维 Gaussian KDE")
    kernel = gaussian_kde(points)
    cov = kernel.covariance        # Σ (2×2)
    inv_cov = np.linalg.inv(cov)
    det_cov = np.linalg.det(cov)
    n = points.shape[1]

    # 3D KDE可视化
    print(">>>> 输出三维KDE结果")
    fig = plt.figure(figsize=(12, 12))
    plt.rcParams['font.sans-serif'] = ['Times New Roman']
    plt.rcParams['font.size'] = 24
    ax = fig.add_subplot(111, projection='3d')

    # 生成网格用于可视化
    xmin, xmax = -15, 15
    ymin, ymax = 0, 30
    x = np.linspace(xmin, xmax, 150)  # 生成精确的X坐标序列
    y = np.linspace(ymin, ymax, 150)  # 生成精确的Y坐标序列
    xx, yy = np.meshgrid(x, y, indexing='ij')  # 创建网格坐标

    positions = np.vstack([xx.ravel(), yy.ravel()])
    zz = np.reshape(kernel(positions), xx.shape)

    # 绘制三维表面图
    surf = ax.plot_surface(xx, yy, zz, cmap='gist_earth',
                          rstride=1, cstride=1,
                          linewidth=0, antialiased=True)

    # 设置颜色条和标签
    fig.colorbar(surf, shrink=0.5, aspect=5, label='Density')
    ax.view_init(elev=30, azim=235)
    ax.set_title(f'{name} 3D KDE Visualization', x=0.62)
    ax.set_xlabel('X Position (relative)')
    ax.set_ylabel('Y Position (relative)')
    ax.set_zlabel('Density')
    ax.set_zlim(0, zz.max()*1.1)  # 自动调整Z轴范围

    # 保存三维图像
    kde_3d_path = os.path.join(sub_folder_path, f"{name}_3DKDE.png")
    plt.savefig(kde_3d_path, dpi=600, bbox_inches='tight')
    plt.close()
    print(f"三维KDE图已保存：{kde_3d_path}")

    # CSV输出（Y轴为行，X轴为列）
    csv_path = os.path.join(sub_folder_path, f"{name}_3DKDE_Grid.csv")
    with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        # 格式化X坐标头为小数（禁用科学计数法）
        formatted_x = [f"{num:.8f}" for num in x]
        writer.writerow(['Y/X'] + formatted_x)

        # 格式化Y坐标和概率值为小数
        for y_val, z_row in zip(y, zz.T):
            formatted_row = [f"{num:.8f}" for num in z_row]
            writer.writerow([f"{y_val:.8f}"] + formatted_row)


if __name__ == "__main__":
    if not os.path.exists(sub_folder_path):
        os.makedirs(sub_folder_path, exist_ok=True)

    build_data_ref()
    execute_analyze(local_pos_all, "Group_Fire")
