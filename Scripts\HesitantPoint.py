import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import os
from pathlib import Path
import xml.etree.ElementTree as ET
from matplotlib.patches import Rectangle
import matplotlib.patches as patches
import seaborn as sns
import chardet
import cairosvg
from PIL import Image
import io

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class HesitantPointAnalyzer:
    def __init__(self, data_root):
        """
        初始化犹豫点分析器
        """
        self.data_root = Path(data_root)
        self.output_dir = self.data_root / "analysis_results" / "behavioral" / "HesitantPoint"
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # 出口位置
        self.left_exit = np.array([51.3, -2.424, -2.872])   # 左侧出口坐标
        self.right_exit = np.array([-228.7, -2.424, -2.769])   # 右侧出口坐标

        # 犹豫点阈值设置
        self.hesitation_threshold = 1.0  # 犹豫点阈值（秒）
        self.exit_distance_threshold = 3  # 距离出口1m以内不绘制

        # 定义决策区边界
        self.decision_zones = {
            'zone1':{
                'bound':[(-45.0, -40.0), (-1.345, 0.801)],
                'title':'DP1'
            },
            'zone2':{
                'bound':[(-45.0, -40.0), (-3.238, -1.869)],
                'title':'DP2'
            },
            'zone3':{
                'bound':[(-36.66, 6.51), (-3.328, -1.53)],
                'title':'DP3'
            },
            'zone4':{ 
                'bound':[(17.99, 39.088), (-3, -1.3)],
                'title':'DP4-Low'
            },
            'zone5':{
                'bound':[(33.472, 39.088), (-3, -1.3)],
                'title':'DP4-Medium'
            },
            'zone6':{
                'bound':[(41.866, 47.62), (-1.3, 1.615)],
                'title':'DP4-High'
            }
        }

        # 统一配色方案
        self.colors = {
            'High': '#2E86AB',      # 蓝色 - 高能见度
            'Medium': '#A23B72',    # 紫色 - 中等能见度
            'Low': '#F18F01',       # 橙色 - 低能见度
            'NPC_0': '#F18F01',     # 蓝色 - 0个NPC
            'NPC_10': '#A23B72',    # 紫色 - 10个NPC
            'NPC_20': '#2E86AB',    # 橙色 - 20个NPC
            'hesitation': '#E74C3C', # 红色 - 犹豫点
            'decision_zone': '#3498DB', # 蓝色 - 决策区
            'exit': '#27AE60'       # 绿色 - 出口
        }

        # 底图路径
        self.background_svg = self.data_root/ "底图.svg"

    def load_behavioral_data(self):
        """加载行为数据分析结果"""
        data_file = self.data_root / "BehavioralData1.csv"
        if not data_file.exists():
            raise FileNotFoundError(f"行为数据文件不存在: {data_file}")

        # 检测文件编码
        with open(data_file, 'rb') as f:
            raw_data = f.read()
            encoding_result = chardet.detect(raw_data)
            detected_encoding = encoding_result['encoding']
            print(f"检测到的文件编码: {detected_encoding}")

        # 尝试多种编码方式读取文件
        encodings_to_try = [detected_encoding, 'gbk', 'gb2312', 'utf-8', 'utf-8-sig', 'latin1']
        df = None

        for encoding in encodings_to_try:
            if encoding is None:
                continue
            try:
                print(f"尝试使用编码: {encoding}")
                df = pd.read_csv(data_file, encoding=encoding)
                print(f"成功使用编码 {encoding} 读取文件")
                break
            except Exception as e:
                print(f"编码 {encoding} 失败: {e}")
                continue

        # 筛选第二部分的实验
        df = df[df['experiment'] == 2]

        return df

    def parse_hesitation_positions(self, position_str):
        """解析犹豫点位置字符串"""
        if pd.isna(position_str) or position_str == '':
            return []

        positions = []
        # 解析格式如 "(x1, y1, z1); (x2, y2, z2)"
        pos_parts = position_str.split(';')
        for part in pos_parts:
            part = part.strip()
            if part.startswith('(') and part.endswith(')'):
                coords = part[1:-1].split(',')
                if len(coords) == 3:
                    try:
                        x, y, z = float(coords[0]), float(coords[1]), float(coords[2])
                        positions.append((x, y, z))
                    except ValueError:
                        continue
        return positions

    def filter_hesitation_points(self, positions):
        """过滤距离出口太近的犹豫点"""
        filtered_positions = []
        for pos in positions:
            x, y, z = pos
            pos_array = np.array([x, y, z])

            # 计算到两个出口的距离
            dist_to_left = np.linalg.norm(pos_array - self.left_exit)
            dist_to_right = np.linalg.norm(pos_array - self.right_exit)

            # 如果距离任一出口小于阈值，则不绘制
            if dist_to_left >= self.exit_distance_threshold and dist_to_right >= self.exit_distance_threshold:
                filtered_positions.append(pos)

        return filtered_positions

    def load_background_image(self, ax):
        """加载SVG底图"""
        try:
            if self.background_svg.exists():
                # 将SVG转换为PNG
                png_data = cairosvg.svg2png(url=str(self.background_svg))

                # 使用PIL加载PNG数据
                img = Image.open(io.BytesIO(png_data))

                # 显示图像作为背景
                # 根据实际坐标系调整图像位置和大小
                ax.imshow(img, extent=[-240, 60, -3.6, 3.6], aspect='auto', alpha=1, zorder=1)

                print("底图加载成功")
                return True
        except Exception as e:
            print(f"加载底图失败: {e}")
        return False

    def draw_tunnel_structures(self, ax):
        """绘制隧道结构"""
        # 绘制隧道主体
        tunnel_rect = Rectangle((-250, -5), 300, 10,
                               facecolor='lightgray', edgecolor='black',
                               alpha=0.3, linewidth=1)
        ax.add_patch(tunnel_rect)

        # 绘制左出口
        left_exit_rect = Rectangle((45, -4), 12, 8,
                                  facecolor=self.colors['exit'], edgecolor='black',
                                  linewidth=2, alpha=0.8, label='LeftExit')
        ax.add_patch(left_exit_rect)

        # 绘制右出口
        right_exit_rect = Rectangle((-240, -4), 12, 8,
                                   facecolor=self.colors['exit'], edgecolor='black',
                                   linewidth=2, alpha=0.8, label='RightExit')
        ax.add_patch(right_exit_rect)

    def draw_decision_zones(self, ax):
        """绘制决策区域"""
        # 添加决策区域配色方案
        decision_colors = {
            'DP1':"#2E68AB", 
            'DP2':"#3B5FA2", 
            'DP3':"#3501F1", 
            'DP4-Low':"#9834DB",
            'DP4-Medium':"#DB34BF",
            'DP4-High':"#DB3453"
        }
        for zone_name, zone_info in self.decision_zones.items():
            x_bounds, z_bounds = zone_info['bound']
            rect = Rectangle((x_bounds[0], z_bounds[0]),
                           x_bounds[1] - x_bounds[0],
                           z_bounds[1] - z_bounds[0],
                           linewidth=2, edgecolor=decision_colors[zone_info['title']],
                           facecolor='none', linestyle='--', alpha=1,
                           label=f"{zone_info['title']}")
            ax.add_patch(rect)

    def plot_single_hesitation_points(self, data, title, color, filename_prefix):
        """绘制单个条件下的犹豫点分布图"""
        fig, ax = plt.subplots(1, 1, figsize=(15, 3))

        # 加载底图或绘制隧道结构
        if not self.load_background_image(ax):
            self.draw_tunnel_structures(ax)

        # 绘制决策区域
        self.draw_decision_zones(ax)

        # 绘制犹豫点
        hesitation_count = 0
        for _, row in data.iterrows():
            positions = self.parse_hesitation_positions(row['Hesitation_position'])
            filtered_positions = self.filter_hesitation_points(positions)

            for pos in filtered_positions:
                x, y, z = pos
                ax.scatter(x, z, c=color, s=80, alpha=0.7,
                         edgecolors='black', linewidth=1, zorder=5)
                hesitation_count += 1

        # 设置图表属性
        ax.set_xlim(-241, 61)
        ax.set_ylim(-5, 5)
        ax.set_xlabel('X_Axis(m)', fontsize=10)
        ax.set_ylabel('Z_Axis(m)', fontsize=10)
        ax.set_title(f'{title}\nNumber Of Hesitate Points: {hesitation_count}',
                    fontsize=12, fontweight='bold', color=color)
        ax.grid(True, alpha=0.3)

        # 添加图例
        ax.scatter([], [], c=color, s=80, alpha=0.7,
                  edgecolors='black', linewidth=1, label='Hesitate Points')
        ax.legend(loc='upper left', ncol=7, fontsize=10)

        plt.tight_layout()

        # 保存图片
        png_path = self.output_dir / f"{filename_prefix}.png"
        svg_path = self.output_dir / f"{filename_prefix}.svg"

        plt.savefig(png_path, dpi=600, bbox_inches='tight', format='png')
        plt.savefig(svg_path, bbox_inches='tight', format='svg')

        print(f"{title}犹豫点图已保存:")
        print(f"PNG: {png_path}")
        print(f"SVG: {svg_path}")

        plt.close()

        return hesitation_count

    def plot_hesitation_points_by_smoke(self):
        """按烟气浓度分别绘制犹豫点（3张独立的图）"""
        df = self.load_behavioral_data()

        # 烟气等级映射
        smoke_mapping = {1: 'Low', 2: 'Medium', 3: 'High'}
        smoke_names = {1: 'Smoke_Level:Low', 2: 'Smoke_Level:Medium', 3: 'Smoke_Level:High'}

        print("按烟气浓度分别绘制犹豫点分布图...")

        for smoke_level, smoke_name in smoke_mapping.items():
            # 筛选数据
            smoke_data = df[df['smoke_level'] == smoke_level]

            # 绘制单个图
            count = self.plot_single_hesitation_points(
                data=smoke_data,
                title=smoke_names[smoke_level],
                color=self.colors[smoke_name],
                filename_prefix=f"hesitation_points_smoke_{smoke_name.lower()}"
            )

    def plot_hesitation_points_by_npc(self):
        """按NPC数量分别绘制犹豫点（3张独立的图）"""
        df = self.load_behavioral_data()

        # NPC数量和对应的名称
        npc_counts = [0, 10, 20]
        npc_names = {0: 'None_NPC', 10: '10_NPC', 20: '20_NPC'}

        print("按NPC数量分别绘制犹豫点分布图...")

        for npc_count in npc_counts:
            # 筛选数据
            npc_data = df[df['npc_count'] == npc_count]

            # 绘制单个图
            color_key = f'NPC_{npc_count}'
            count = self.plot_single_hesitation_points(
                data=npc_data,
                title=npc_names[npc_count],
                color=self.colors[color_key],
                filename_prefix=f"hesitation_points_npc_{npc_count}"
            )

    def run_analysis(self):
        """运行完整的犹豫点分析"""
        print("开始犹豫点分析...")

        try:
            # 按烟气浓度分组绘制
            print("绘制按烟气浓度分组的犹豫点分布图...")
            self.plot_hesitation_points_by_smoke()

            # 按NPC数量分组绘制
            print("绘制按NPC数量分组的犹豫点分布图...")
            self.plot_hesitation_points_by_npc()

            print("犹豫点分析完成！")

        except Exception as e:
            print(f"分析过程中出现错误: {e}")
            import traceback
            traceback.print_exc()

def get_grand_parent_folder():
    """获取数据根目录"""
    current_script_path = os.path.abspath(__file__)
    current_folder = os.path.dirname(current_script_path)
    parent_folder = os.path.dirname(current_folder)
    return parent_folder

def get_target_file_path():
    """获取目标文件路径"""
    return get_grand_parent_folder() + os.sep + "ScriptsTest"

if __name__ == "__main__":
    data_root = get_target_file_path()
    analyzer = HesitantPointAnalyzer(data_root)
    analyzer.run_analysis()