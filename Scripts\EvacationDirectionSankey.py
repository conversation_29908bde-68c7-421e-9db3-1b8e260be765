import pandas as pd
import plotly.graph_objects as go
import plotly.io as pio
import os
from pathlib import Path
import chardet

def create_evacuation_direction_sankey(data_root):
    """
    创建疏散方向选择的桑基图
    基于实验数据中的决策链条：
    DP1-Out-DP2-R-ExitSelection-R
    DP1-Out-DP2-L-DP3-Re-ExitSelection-R
    DP1-Out-DP2-L-DP3-NoRe-DP4-Re-ExitSelection-R
    DP1-Out-DP2-L-DP3-NoRe-DP4-NoRe-ExitSelection-L
    DP1-In-DP3-Re-ExitSelection-R
    DP1-In-DP3-NoRe-DP4-Re-ExitSelection-R
    DP1-In-DP3-NoRe-DP4-NoRe-ExitSelection-L
    DP1-In-ExitSelection-R
    """

    # 读取数据
    data_root = Path(data_root)
    data_path = data_root / "DirectionalChoice.csv"

    # 检测文件编码
    with open(data_path, 'rb') as f:
        raw_data = f.read()
        encoding_result = chardet.detect(raw_data)
        detected_encoding = encoding_result['encoding']
        print(f"检测到的文件编码: {detected_encoding}")

    # 尝试多种编码方式读取文件
    encodings_to_try = [detected_encoding, 'gbk', 'gb2312', 'utf-8', 'utf-8-sig', 'latin1']
    df = None

    for encoding in encodings_to_try:
        if encoding is None:
            continue
        try:
            print(f"尝试使用编码: {encoding}")
            df = pd.read_csv(data_path, encoding=encoding)
            print(f"成功使用编码 {encoding} 读取文件")
            break
        except Exception as e:
            print(f"编码 {encoding} 失败: {e}")
            continue

    if df is None:
        raise ValueError("无法读取CSV文件，请检查文件编码")

    # 获取总计行数据
    total_row = df[df['Scenarios'] == 'Total'].iloc[0]
    print("数据读取成功，总计行数据：")
    print(total_row)

    # 提取各决策点的数据
    dp1_in = int(total_row['In'])      # 44
    dp1_out = int(total_row['Out'])    # 89
    dp2_l = int(total_row['L'])        # 64
    dp2_r = int(total_row['R'])        # 25
    dp3_re = int(total_row['Re'])      # 32
    dp3_nore = int(total_row['NoRe'])  # 46
    dp4_re = int(total_row['Re.1'])    # 33
    dp4_nore = int(total_row['NoRe.1']) # 14
    exit_l = int(total_row['L.1'])     # 14
    exit_r = int(total_row['R.1'])     # 120

    print(f"\n数据统计:")
    print(f"DP1: In={dp1_in}, Out={dp1_out}")
    print(f"DP2: L={dp2_l}, R={dp2_r}")
    print(f"DP3: Re={dp3_re}, NoRe={dp3_nore}")
    print(f"DP4: Re={dp4_re}, NoRe={dp4_nore}")
    print(f"Exit: L={exit_l}, R={exit_r}")

    # 根据决策链条分析流量分配
    # 从数据分析可知：
    # DP3总流量 = DP2-L + DP1-In的一部分
    # DP3总流量 = dp3_re + dp3_nore = 32 + 46 = 78
    # DP2-L = 64，所以从DP1-In到DP3的流量 = 78 - 64 = 14
    # 从DP1-In直接到ExitSelection的流量 = 44 - 14 = 30

    dp1_in_to_dp3 = max(0, (dp3_re + dp3_nore) - dp2_l)  # 14
    dp1_in_to_exit = dp1_in - dp1_in_to_dp3  # 30

    print(f"\n流量分配计算:")
    print(f"DP1-In到DP3: {dp1_in_to_dp3}")
    print(f"DP1-In直接到Exit: {dp1_in_to_exit}")

    # 定义节点标签 - 简化为5层
    node_labels = [
        # 第1层：DP1
        "DP1-Out",               # 0
        "DP1-In",                # 1

        # 第2层：DP2
        "DP2-L",                 # 2
        "DP2-R",                 # 3

        # 第3层：DP3
        "DP3-Re",                # 4
        "DP3-NoRe",              # 5

        # 第4层：DP4
        "DP4-Re",                # 6
        "DP4-NoRe",              # 7

        # 第5层：ExitSelection
        "Exit-L",                # 8
        "Exit-R"                 # 9
    ]

    # 定义连接关系和流量 - 简化版本
    source_nodes = []
    target_nodes = []
    flow_values = []
    flow_labels = []

    # 第1层到第2层：DP1 -> DP2
    # DP1-Out -> DP2-L, DP2-R
    source_nodes.extend([0, 0])  # DP1-Out
    target_nodes.extend([2, 3])  # DP2-L, DP2-R
    flow_values.extend([dp2_l, dp2_r])
    flow_labels.extend([f"DP1-Out→DP2-L ({dp2_l})", f"DP1-Out→DP2-R ({dp2_r})"])

    # 第1层到第3层：DP1-In -> DP3 (部分)
    if dp1_in_to_dp3 > 0:
        source_nodes.append(1)  # DP1-In
        target_nodes.append(4)  # DP3-Re (简化，实际可能分流到Re和NoRe)
        flow_values.append(dp1_in_to_dp3)
        flow_labels.append(f"DP1-In→DP3 ({dp1_in_to_dp3})")

    # 第1层到第5层：DP1-In -> Exit-R (直接)
    if dp1_in_to_exit > 0:
        source_nodes.append(1)  # DP1-In
        target_nodes.append(9)  # Exit-R
        flow_values.append(dp1_in_to_exit)
        flow_labels.append(f"DP1-In→Exit-R ({dp1_in_to_exit})")

    # 第2层到第3层：DP2-L -> DP3
    # 将DP2-L的流量分配到DP3-Re和DP3-NoRe
    dp2l_to_dp3_re = int(dp2_l * dp3_re / (dp3_re + dp3_nore))
    dp2l_to_dp3_nore = dp2_l - dp2l_to_dp3_re

    source_nodes.extend([2, 2])  # DP2-L
    target_nodes.extend([4, 5])  # DP3-Re, DP3-NoRe
    flow_values.extend([dp2l_to_dp3_re, dp2l_to_dp3_nore])
    flow_labels.extend([f"DP2-L→DP3-Re ({dp2l_to_dp3_re})", f"DP2-L→DP3-NoRe ({dp2l_to_dp3_nore})"])

    # 第2层到第5层：DP2-R -> Exit-R (直接)
    source_nodes.append(3)  # DP2-R
    target_nodes.append(9)  # Exit-R
    flow_values.append(dp2_r)
    flow_labels.append(f"DP2-R→Exit-R ({dp2_r})")

    # 第3层到第4层：DP3-NoRe -> DP4
    # 将DP3-NoRe的流量分配到DP4-Re和DP4-NoRe
    source_nodes.extend([5, 5])  # DP3-NoRe
    target_nodes.extend([6, 7])  # DP4-Re, DP4-NoRe
    flow_values.extend([dp4_re, dp4_nore])
    flow_labels.extend([f"DP3-NoRe→DP4-Re ({dp4_re})", f"DP3-NoRe→DP4-NoRe ({dp4_nore})"])

    # 第3层到第5层：DP3-Re -> Exit-R
    source_nodes.append(4)  # DP3-Re
    target_nodes.append(9)  # Exit-R
    flow_values.append(dp3_re)
    flow_labels.append(f"DP3-Re→Exit-R ({dp3_re})")

    # 第4层到第5层：DP4 -> Exit
    source_nodes.extend([6, 7])  # DP4-Re, DP4-NoRe
    target_nodes.extend([9, 8])  # Exit-R, Exit-L
    flow_values.extend([dp4_re, dp4_nore])
    flow_labels.extend([f"DP4-Re→Exit-R ({dp4_re})", f"DP4-NoRe→Exit-L ({dp4_nore})"])

    # 计算最终右出口的总流量
    exit_r_flow = dp1_in_to_exit + dp2_r + dp3_re + dp4_re

    print(f"\n桑基图流量验证:")
    print(f"总人数: {dp1_in + dp1_out}")
    print(f"最终左出口: {dp4_nore}")
    print(f"最终右出口: {exit_r_flow}")
    print(f"总出口人数: {dp4_nore + exit_r_flow}")
    print(f"数据一致性检查: {(dp1_in + dp1_out) == (dp4_nore + exit_r_flow)}")

    # 四角配色方案：蓝色、绿色、橙色、紫色、红色
    layer_colors = {
        1: "#3498db",  # 蓝色 - DP1层
        2: "#2ecc71",  # 绿色 - DP2层
        3: "#f39c12",  # 橙色 - DP3层
        4: "#9b59b6",  # 紫色 - DP4层
        5: "#e74c3c"   # 红色 - Exit层
    }

    # 创建桑基图
    fig = go.Figure(data=[go.Sankey(
        node=dict(
            pad=15,
            thickness=20,
            line=dict(color="white", width=0.5),
            label=node_labels,
            color=[
                layer_colors[1],  # DP1-Out
                layer_colors[1],  # DP1-In
                layer_colors[2],  # DP2-L
                layer_colors[2],  # DP2-R
                layer_colors[3],  # DP3-Re
                layer_colors[3],  # DP3-NoRe
                layer_colors[4],  # DP4-Re
                layer_colors[4],  # DP4-NoRe
                layer_colors[5],  # Exit-L
                layer_colors[5]   # Exit-R
            ]
        ),
        link=dict(
            source=source_nodes,
            target=target_nodes,
            value=flow_values,
            color=[
                "rgba(52,152,219,0.4)" if "DP1" in label  # 蓝色透明
                else "rgba(46,204,113,0.4)" if "DP2" in label  # 绿色透明
                else "rgba(243,156,18,0.4)" if "DP3" in label  # 橙色透明
                else "rgba(155,89,182,0.4)" if "DP4" in label  # 紫色透明
                else "rgba(231,76,60,0.4)"  # 红色透明
                for label in flow_labels
            ],
            label=flow_labels
        )
    )])

    fig.update_layout(
        title={
            'text': "疏散方向选择桑基图<br><sub>Evacuation Direction Choice Sankey Diagram</sub>",
            'x': 0.5,
            'xanchor': 'center',
            'font': {'size': 20}
        },
        font_size=12,
        width=1400,
        height=900,
        margin=dict(l=50, r=50, t=100, b=50)
    )

    # 确保输出目录存在
    output_dir = data_root / "analysis_results" / "behavioral"
    os.makedirs(output_dir, exist_ok=True)

    # 保存为PNG和SVG格式
    png_path = output_dir / "EvacuationDircetionSankey.png"
    svg_path = output_dir / "EvacuationDircetionSankey.svg"
    html_path = output_dir / "EvacuationDircetionSankey.html"

    try:
        # 保存PNG
        pio.write_image(fig, png_path, format='png', width=1400, height=900, scale=2)
        print(f"桑基图已保存为PNG格式: {png_path}")

        # 保存SVG
        pio.write_image(fig, svg_path, format='svg', width=1400, height=900)
        print(f"桑基图已保存为SVG格式: {svg_path}")

        # 保存HTML（交互式版本）
        fig.write_html(html_path)
        print(f"桑基图已保存为HTML格式: {html_path}")

    except Exception as e:
        print(f"保存图像时出错: {e}")
        print("尝试只保存HTML格式...")
        fig.write_html(html_path)
        print(f"桑基图已保存为HTML格式: {html_path}")

    # 显示图表
    fig.show()

    return fig

def get_grand_parent_folder():
    """获取脚本文件的父目录"""
    current_script_path = os.path.abspath(__file__)
    current_folder = os.path.dirname(current_script_path)
    parent_folder = os.path.dirname(current_folder)
    return parent_folder


def get_target_file_path():
    """获取目标文件路径"""
    return get_grand_parent_folder() + os.sep + "ScriptsTest"


if __name__ == "__main__":
    print("开始创建疏散方向选择桑基图...")
    data_root = get_target_file_path()
    print(f"数据根目录: {data_root}")

    try:
        fig = create_evacuation_direction_sankey(data_root)
        print("桑基图创建完成！")
    except Exception as e:
        print(f"创建桑基图时发生错误: {e}")
        import traceback
        traceback.print_exc()
