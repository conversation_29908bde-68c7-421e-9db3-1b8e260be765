import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import math
from scipy.spatial.distance import euclidean

class PathComplexityAssessment:
    """疏散路径复杂度评估器"""

    def __init__(self, data_root):
        self.data_root = Path(data_root)
        self.output_dir = self.data_root / "analysis_results" / "behavioral" / "path_complexity"
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"路径复杂度评估文件路径为：{self.output_dir}")
        
        # 镜像参考点
        self.mirror_point = (-92.5, -0.4318, -0.4991)
        
        # 出口坐标
        self.left_exit = np.array([51.3, -2.424, -2.872])
        self.right_exit = np.array([-228.7, -2.424, -2.769])

        # 理想路径长度
        self.ideal_Path_Length = 190 # m
        
        # 转向阈值
        self.turn_threshold = 70.0  # 度

    def analyze_path_complexity(self):
        """路劲复杂度分析"""

        print("###开始分析疏散路径复杂度###")

        # 读取行为数据文件
        behavioral_file = self.data_root / "BehavioralData1.csv"

        if not behavioral_file:
            print("未找到行为数据文件BehavioralData.csv")
            return

        df = pd.read_csv(behavioral_file, encoding='utf-8-sig', engine='python')

        # 筛选第二部分实验数据
        df_exp2 = df[df['experiment'] == 2].copy()

        if len(df_exp2) == 0:
            print("未找到第二次行为数据")
            return
        
        print(f"找到{len(df_exp2)}条第二次行为数据")

        # 存储结果
        results = []

        # 全量分组
        smoke_npc_combinations = [
            (1, 0), (1, 10), (1, 20),   # 低浓度
            (2, 0), (2, 10), (2, 20),   # 中浓度  
            (3, 0), (3, 10), (3, 20)    # 高浓度
        ]

        for smoke_level, npc_count in smoke_npc_combinations:
            # 筛选特定烟气浓度和NPC数量的数据
            condition_data = df_exp2[(df_exp2['smoke_level'] == smoke_level) & 
                (df_exp2['npc_count'] == npc_count)
            ].copy()

            if len(condition_data) == 0:
                print(f"烟气等级{smoke_level}+NPC{npc_count}的数据为空，跳过")
                continue

            print(f"开始分析烟气等级{smoke_level}+NPC{npc_count}的数据")

            # 计算改组的复杂度指标
            group_metrics = self.calculate_group_metrics(condition_data, smoke_level, npc_count)

            if group_metrics:
                results.append(group_metrics)
        
        # 保存结果
        if results:
            self.save_results(results)
        else:
            print("没有有效的分析结果")
            
        print("###路径复杂度分析完成###")

    def calculate_group_metrics(self, condition_data, smoke_level, npc_count):
        """计算单组的路径复杂度指标"""

        path_efficiencies = []
        turn_counts = []
        fractal_dimensions = []

        for _, row in condition_data.iterrows():
            # 加载并处理轨迹数据
            trajectory = self.load_trajectory_data(row)

            if trajectory is None:
                print("加载轨迹数据失败，跳过")
                continue
            
            # 1.计算路径效率
            path_efficiency = self.calculate_path_efficiency(trajectory, row)
            if path_efficiency is not None:
                path_efficiencies.append(path_efficiency)
            
            # 2.计算转弯次数
            turn_count = self.calculate_turn_count(trajectory)
            turn_counts.append(turn_count)
            
            # 3.计算分形维数
            fractal_dim = self.calculate_fractal_dimension(trajectory)
            if fractal_dim is not None:
                fractal_dimensions.append(fractal_dim)

        # 计算平均值
        if path_efficiencies and turn_counts and fractal_dimensions:
            # 获取烟气等级
            smoke_name = {1: 'Low', 2: 'Medium', 3: 'High'}[smoke_level]

            return{
                '分组': f'Smoke_Level:{smoke_name} & NPC_Number:{npc_count}',
                '平均路径效率': round(np.mean(path_efficiencies), 4),
                '平均转弯次数': round(np.mean(turn_counts), 2),
                '平均分形维数': round(np.mean(fractal_dimensions), 4)
            }
        
        return None
    
    def load_trajectory_data(self, row):
        """加载轨迹数据"""
        file_path = Path(row['file_path'])
        organized_file_path = file_path.parent / f"{file_path.stem}.csv" 

        if not organized_file_path.exists():
            print(f"文件{organized_file_path.name}不存在")
            return None
        
        try:
            df = pd.read_csv(organized_file_path, encoding='utf-8-sig', engine='python')
            
            # 检查必要列
            required_cols = ['PosX', 'PosZ']
            missing_cols = [col for col in required_cols if col not in df.columns]
            if missing_cols:
                print(f"缺少必要列: {missing_cols}")
                return None
            
            pos_x = df['PosX'].values
            pos_z = df['PosZ'].values
            
            # 处理镜像条件
            if row['is_mirror']:
                pos_x = 2 * self.mirror_point[0] - pos_x
            
            return {
                'x': pos_x,
                'z': pos_z
            }
        except Exception as e:
            print(f"处理轨迹数据时出错: {e}")
            return None
        
    def calculate_path_efficiency(self, trajectory, row):
        """计算路径效率 = 理想路径长度 / 实际路径长度"""

        try:
            # 实际路径长度
            actual_distance = row['evacuation_distance']
            # 理想路径长度
            ideal_distance = self.ideal_Path_Length
            # 计算路径效率
            return ideal_distance / actual_distance
        except Exception as e:
            print(f"计算路径效率时出错: {e}")
        
        return None
    
    def calculate_turn_count(self,trajectory):
        """计算转弯次数"""
        try:
            x_coords = trajectory['x']
            z_coords = trajectory['z']
            
            if len(x_coords) < 3:
                return 0
            
            turn_count = 0
            
            for i in range(1, len(x_coords) - 1):
                # 计算三个连续点的向量
                v1 = np.array([x_coords[i] - x_coords[i-1], z_coords[i] - z_coords[i-1]])
                v2 = np.array([x_coords[i+1] - x_coords[i], z_coords[i+1] - z_coords[i]])
                
                # 计算向量长度
                norm_v1 = np.linalg.norm(v1)
                norm_v2 = np.linalg.norm(v2)
                
                if norm_v1 > 0 and norm_v2 > 0:
                    # 计算夹角
                    cos_angle = np.dot(v1, v2) / (norm_v1 * norm_v2)
                    cos_angle = np.clip(cos_angle, -1, 1)  # 防止数值误差
                    angle_deg = np.degrees(np.arccos(cos_angle))
                    
                    # 如果角度变化超过阈值，记录为一次转弯
                    if angle_deg > self.turn_threshold:
                        turn_count += 1
            
            return turn_count
            
        except Exception as e:
            print(f"计算转弯次数时出错: {e}")
            return 0
        
    def calculate_fractal_dimension(self, trajectory):
        """计算分形维数(使用盒计数法)"""
        try:
            x_coords = trajectory['x']
            z_coords = trajectory['z']
            
            if len(x_coords) < 4:
                return None
            
            # 将轨迹点组合成坐标对
            points = np.column_stack((x_coords, z_coords))
            
            # 计算边界框
            min_x, min_z = np.min(points, axis=0)
            max_x, max_z = np.max(points, axis=0)
            
            # 不同的盒子大小
            box_sizes = []
            box_counts = []
            
            # 从大到小的盒子尺寸
            max_range = max(max_x - min_x, max_z - min_z)
            if max_range <= 0:
                return None
                
            for i in range(3, 8):  # 使用5个不同的盒子大小
                box_size = max_range / (2 ** i)
                if box_size <= 0:
                    continue
                    
                # 计算需要多少个盒子来覆盖轨迹
                n_boxes_x = int(np.ceil((max_x - min_x) / box_size))
                n_boxes_z = int(np.ceil((max_z - min_z) / box_size))
                
                # 创建网格
                occupied_boxes = set()
                
                for x, z in points:
                    box_x = int((x - min_x) / box_size)
                    box_z = int((z - min_z) / box_size)
                    occupied_boxes.add((box_x, box_z))
                
                box_sizes.append(box_size)
                box_counts.append(len(occupied_boxes))
            
            if len(box_sizes) < 3:
                return None
            
            # 使用线性回归计算分形维数
            log_sizes = np.log(box_sizes)
            log_counts = np.log(box_counts)
            
            # 计算斜率（分形维数的负值）
            coeffs = np.polyfit(log_sizes, log_counts, 1)
            fractal_dimension = -coeffs[0]
            
            return fractal_dimension
            
        except Exception as e:
            print(f"计算分形维数时出错: {e}")
            return None
        
    def save_results(self, results):
        """保存结果到CSV文件"""
        df_results = pd.DataFrame(results)
        
        # 保存到CSV文件
        output_file = self.data_root / "PathComplexity.csv"
        df_results.to_csv(output_file, index=False, encoding='utf-8-sig')
        
        print(f"路径复杂度分析结果已保存: {output_file}")
        print("\n结果预览:")
        print(df_results.to_string(index=False))

def get_grand_parent_folder():
    import os
    current_script_path = os.path.abspath(__file__)
    current_folder = os.path.dirname(current_script_path)
    parent_folder = os.path.dirname(current_folder)
    return parent_folder

def get_target_file_path():
    import os
    return get_grand_parent_folder() + os.sep + "ScriptsTest"

if __name__ == "__main__":
    # 创建分析器实例
    analyzer = PathComplexityAssessment(get_target_file_path())
    
    # 分析路径复杂度
    analyzer.analyze_path_complexity()