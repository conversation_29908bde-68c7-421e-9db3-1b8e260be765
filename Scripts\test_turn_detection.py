import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import sys

# 添加当前目录到路径
sys.path.append(str(Path(__file__).parent))
from PathComplexityAssessment import PathComplexityAssessment

class TurnDetectionTester:
    """转弯检测方法测试器"""
    
    def __init__(self, data_root):
        self.data_root = Path(data_root)
        self.analyzer = PathComplexityAssessment(data_root)
        
    def test_turn_detection_methods(self, sample_size=5):
        """测试不同的转弯检测方法"""
        
        print("###开始测试转弯检测方法###")
        
        # 读取行为数据文件
        behavioral_file = self.data_root / "BehavioralData1.csv"
        
        if not behavioral_file.exists():
            print("未找到行为数据文件BehavioralData1.csv")
            return
        
        df = pd.read_csv(behavioral_file, encoding='utf-8-sig', engine='python')
        df_exp2 = df[df['experiment'] == 2].copy()
        
        if len(df_exp2) == 0:
            print("未找到第二次行为数据")
            return
        
        # 随机选择一些样本进行测试
        sample_data = df_exp2.sample(min(sample_size, len(df_exp2)))
        
        results = []
        
        for idx, row in sample_data.iterrows():
            print(f"\n测试样本 {idx}: 烟气等级{row['smoke_level']}, NPC数量{row['npc_count']}")
            
            # 加载轨迹数据
            trajectory = self.analyzer.load_trajectory_data(row)
            if trajectory is None:
                print("加载轨迹数据失败，跳过")
                continue
            
            # 测试原始方法（三点法）
            original_count = self._calculate_turn_count_original(trajectory)
            
            # 测试滑动窗口方法
            self.analyzer.turn_detection_method = 'window'
            window_count = self.analyzer.calculate_turn_count(trajectory)
            
            # 测试累积角度方法
            cumulative_count = self.analyzer.calculate_turn_count_alternative(trajectory)
            
            result = {
                'sample_id': idx,
                'smoke_level': row['smoke_level'],
                'npc_count': row['npc_count'],
                'trajectory_length': len(trajectory['x']),
                'original_method': original_count,
                'window_method': window_count,
                'cumulative_method': cumulative_count
            }
            
            results.append(result)
            
            print(f"  原始方法（三点法）: {original_count} 次转弯")
            print(f"  滑动窗口方法: {window_count} 次转弯")
            print(f"  累积角度方法: {cumulative_count} 次转弯")
        
        # 保存和显示结果
        if results:
            self._save_and_display_results(results)
        
        print("\n###转弯检测方法测试完成###")
    
    def _calculate_turn_count_original(self, trajectory):
        """原始的三点转弯检测方法"""
        try:
            x_coords = trajectory['x']
            z_coords = trajectory['z']
            
            if len(x_coords) < 3:
                return 0
            
            turn_count = 0
            
            for i in range(1, len(x_coords) - 1):
                # 计算三个连续点的向量
                v1 = np.array([x_coords[i] - x_coords[i-1], z_coords[i] - z_coords[i-1]])
                v2 = np.array([x_coords[i+1] - x_coords[i], z_coords[i+1] - z_coords[i]])
                
                # 计算向量长度
                norm_v1 = np.linalg.norm(v1)
                norm_v2 = np.linalg.norm(v2)
                
                if norm_v1 > 0 and norm_v2 > 0:
                    # 计算夹角
                    cos_angle = np.dot(v1, v2) / (norm_v1 * norm_v2)
                    cos_angle = np.clip(cos_angle, -1, 1)
                    angle_deg = np.degrees(np.arccos(cos_angle))
                    
                    # 如果角度变化超过阈值，记录为一次转弯
                    if angle_deg > self.analyzer.turn_threshold:
                        turn_count += 1
            
            return turn_count
            
        except Exception as e:
            print(f"计算转弯次数（原始方法）时出错: {e}")
            return 0
    
    def _save_and_display_results(self, results):
        """保存和显示测试结果"""
        df_results = pd.DataFrame(results)
        
        # 保存结果
        output_file = self.data_root / "turn_detection_comparison.csv"
        df_results.to_csv(output_file, index=False, encoding='utf-8-sig')
        
        print(f"\n测试结果已保存: {output_file}")
        print("\n结果对比:")
        print(df_results.to_string(index=False))
        
        # 计算统计信息
        print("\n统计信息:")
        print(f"原始方法平均转弯次数: {df_results['original_method'].mean():.2f}")
        print(f"滑动窗口方法平均转弯次数: {df_results['window_method'].mean():.2f}")
        print(f"累积角度方法平均转弯次数: {df_results['cumulative_method'].mean():.2f}")
        
        # 创建对比图表
        self._create_comparison_plot(df_results)
    
    def _create_comparison_plot(self, df_results):
        """创建对比图表"""
        try:
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
            
            # 图1: 各方法转弯次数对比
            methods = ['original_method', 'window_method', 'cumulative_method']
            method_names = ['原始方法', '滑动窗口', '累积角度']
            
            x = np.arange(len(df_results))
            width = 0.25
            
            for i, (method, name) in enumerate(zip(methods, method_names)):
                ax1.bar(x + i * width, df_results[method], width, label=name)
            
            ax1.set_xlabel('样本编号')
            ax1.set_ylabel('转弯次数')
            ax1.set_title('不同方法的转弯次数对比')
            ax1.set_xticks(x + width)
            ax1.set_xticklabels([f'样本{i}' for i in range(len(df_results))])
            ax1.legend()
            ax1.grid(True, alpha=0.3)
            
            # 图2: 方法间相关性
            ax2.scatter(df_results['original_method'], df_results['window_method'], 
                       alpha=0.7, label='原始 vs 滑动窗口')
            ax2.scatter(df_results['original_method'], df_results['cumulative_method'], 
                       alpha=0.7, label='原始 vs 累积角度')
            ax2.plot([0, df_results['original_method'].max()], 
                    [0, df_results['original_method'].max()], 'k--', alpha=0.5)
            ax2.set_xlabel('原始方法转弯次数')
            ax2.set_ylabel('其他方法转弯次数')
            ax2.set_title('方法间相关性')
            ax2.legend()
            ax2.grid(True, alpha=0.3)
            
            plt.tight_layout()
            
            # 保存图表
            plot_file = self.data_root / "turn_detection_comparison.png"
            plt.savefig(plot_file, dpi=300, bbox_inches='tight')
            print(f"对比图表已保存: {plot_file}")
            
            plt.show()
            
        except Exception as e:
            print(f"创建对比图表时出错: {e}")

def get_grand_parent_folder():
    import os
    current_script_path = os.path.abspath(__file__)
    current_folder = os.path.dirname(current_script_path)
    parent_folder = os.path.dirname(current_folder)
    return parent_folder

def get_target_file_path():
    import os
    return get_grand_parent_folder() + os.sep + "ScriptsTest"

if __name__ == "__main__":
    # 创建测试器实例
    tester = TurnDetectionTester(get_target_file_path())
    
    # 运行测试
    tester.test_turn_detection_methods(sample_size=10)
